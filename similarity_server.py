import logging
import os
import pickle
from typing import Dict, List
import numpy as np
from coldstart.stage5 import task_grouping
from entity.entitys import TaskConfig
from multi_task_client import MultiTaskClient


class SimilarityServer:

    def __init__(
        self,
        similarity_threshold: float = 0.5,
        task_configs: Dict[str, TaskConfig] = None,
    ):
        self.logger = logging.getLogger(f"SimilarityServer")

        self.client_similarity = {}
        self.similarity_threshold = similarity_threshold
        self.task_configs = task_configs

        # Similarity cache settings
        self.similarity_cache_dir = "cache/similarity"
        os.makedirs(self.similarity_cache_dir, exist_ok=True)
        self.similarity_cache_file = os.path.join(
            self.similarity_cache_dir, f"similarity_server.pkl"
        )
        self.logger.info(f"Similarity cache file: {self.similarity_cache_file}")

    def add_client_similarity(self, client_id, similarity_matrix, distance_matrix):
        """Add client similarity to server"""
        self.logger.info(f"Adding client {client_id} similarity to server")
        self.client_similarity[client_id] = {
            "similarity_matrix": similarity_matrix,
            "distance_matrix": distance_matrix,
        }

    def _load_similarity_from_cache(self) -> bool:
        """Load similarity matrix from cache file if it exists"""
        if not self.similarity_cache_file or not os.path.exists(
            self.similarity_cache_file
        ):
            self.logger.info("No similarity cache file found in server")
            return False

        try:
            with open(self.similarity_cache_file, "rb") as f:
                cache_data = pickle.load(f)

            self.similarity_matrix = cache_data["similarity_matrix"]
            self.shared_tasks = cache_data["shared_tasks"]
            self.independent_tasks = cache_data["independent_tasks"]
            self.similarity_threshold = cache_data["similarity_threshold"]

            return True

        except Exception as e:
            self.logger.error(f"Failed to load similarity cache in server: {e}")
            return False

    def _save_similarity_to_cache(self):
        """Save similarity matrix to cache file"""
        if not self.similarity_cache_file:
            return

        cache_data = {
            "similarity_matrix": self.similarity_matrix,
            "task_configs": {
                name: {
                    "name": config.name,
                    "task_type": config.task_type.value,
                    "model_type": config.model_type,
                }
                for name, config in self.task_configs.items()
            },
            "shared_tasks": self.shared_tasks,
            "independent_tasks": self.independent_tasks,
            "similarity_threshold": self.similarity_threshold,
        }

        with open(self.similarity_cache_file, "wb") as f:
            pickle.dump(cache_data, f)

        self.logger.info(
            f"Saved similarity matrix to cache: {self.similarity_cache_file}"
        )

    def _determine_task_grouping(
        self, clients: List[MultiTaskClient], force_recompute: bool = False
    ):
        """
        基于所有客户端相似性矩阵的加权聚合来确定任务分组

        Args:
            clients: 客户端列表
        """
        if not force_recompute and self._load_similarity_from_cache():
            self.logger.info(f"Task grouping completed in server:")
            self.logger.info(f"  Similarity threshold: {self.similarity_threshold}")
            self.logger.info(f"  Shared task groups: {self.shared_tasks}")
            self.logger.info(f"  Independent tasks: {self.independent_tasks}")
            self._assign_task_groups_to_clients(clients)
        else:
            # 1. 对所有客户端的相似性矩阵进行加权聚合
            aggregated_similarity_matrix = self._aggregate_client_similarity_matrices(
                clients
            )

            if aggregated_similarity_matrix is None:
                raise ValueError("Failed to aggregate similarity matrices")

            # 2. 保存聚合后的相似性矩阵
            self.similarity_matrix = aggregated_similarity_matrix

            # 3. 基于聚合后的相似性矩阵和阈值进行任务分组
            self._group_tasks_by_similarity()

            # 4. 保存分组结果到缓存文件
            self._save_similarity_to_cache()

            # 5. 记录分组结果
            self.logger.info(f"Task grouping completed:")
            self.logger.info(f"  Similarity threshold: {self.similarity_threshold}")
            self.logger.info(f"  Shared task groups: {self.shared_tasks}")
            self.logger.info(f"  Independent tasks: {self.independent_tasks}")

            # 6. 将分组结果分配给客户端
            self._assign_task_groups_to_clients(clients)

    def _aggregate_client_similarity_matrices(self, clients: List[MultiTaskClient]):
        """
        对所有客户端的相似性矩阵进行加权聚合

        Args:
            clients: 客户端列表

        Returns:
            聚合后的相似性矩阵
        """

        # 获取任务数量
        task_names = list(self.task_configs.keys())
        n_tasks = len(task_names)

        if n_tasks == 0:
            return None

        # 收集所有客户端的相似性矩阵和权重
        similarity_matrices = []
        client_weights = []
        valid_clients = []

        for client in clients:
            client_id = client.client_id
            if client_id in self.client_similarity:
                similarity_matrix = self.client_similarity[client_id][
                    "similarity_matrix"
                ]
                # TODO 打印出相似性矩阵，不用科学计数法
                # np.set_printoptions(suppress=True)  # 不用科学计数法
                # self.logger.info(f"Similarity matrix:\n{similarity_matrix}")

                # 检查矩阵维度
                if similarity_matrix.shape == (n_tasks, n_tasks):
                    similarity_matrices.append(similarity_matrix)

                    # 计算客户端权重（基于数据量）
                    weight = self._calculate_client_weight(client)
                    client_weights.append(weight)
                    valid_clients.append(client_id)
                else:
                    self.logger.warning(
                        f"Client {client_id} similarity matrix shape mismatch: "
                        f"expected ({n_tasks}, {n_tasks}), got {similarity_matrix.shape}"
                    )

        if not similarity_matrices:
            raise ValueError("No valid similarity matrices found")

        # 归一化权重
        total_weight = sum(client_weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in client_weights]
        else:
            normalized_weights = [1.0 / len(client_weights)] * len(client_weights)

        self.logger.info(
            f"Aggregating similarity matrices from {len(valid_clients)} clients"
        )
        self.logger.debug(
            f"Client weights: {dict(zip(valid_clients, normalized_weights))}"
        )

        # 执行加权聚合
        aggregated_matrix = np.zeros((n_tasks, n_tasks))
        for matrix, weight in zip(similarity_matrices, normalized_weights):
            aggregated_matrix += weight * matrix

        self.logger.info("Similarity matrix aggregation completed")
        self.logger.debug(f"Aggregated similarity matrix:\n{aggregated_matrix}")

        return aggregated_matrix

    def _calculate_client_weight(self, client) -> float:
        """
        计算客户端在聚合中的权重（基于数据量）

        Args:
            client: 客户端对象

        Returns:
            客户端权重
        """
        try:
            # 计算客户端所有任务的总数据量
            total_samples = 0
            if hasattr(client, "task_data_loaders"):
                for task_name, (train_loader, _) in client.task_data_loaders.items():
                    if train_loader is not None:
                        total_samples += len(train_loader.dataset)

            # 如果无法获取数据量，使用默认权重
            if total_samples == 0:
                return 1.0  # 默认权重

            return float(total_samples)

        except Exception as e:
            self.logger.warning(
                f"Failed to calculate weight for client {client.client_id}: {e}"
            )
            return 1.0  # 默认权重

    def _group_tasks_by_similarity(self):
        """
        基于聚合后的相似性矩阵和阈值进行任务分组
        """
        if self.similarity_matrix is None:
            self.independent_tasks = list(self.task_configs.keys())
            self.shared_tasks = []
            return

        task_names = list(self.task_configs.keys())
        n_tasks = len(task_names)

        self.shared_tasks = []
        self.independent_tasks = []
        # TODO 打印出相似性矩阵，不用科学计数法
        np.set_printoptions(suppress=True)  # 不用科学计数法
        self.logger.info(f"Similarity matrix:\n{self.similarity_matrix}")
        grouped_list = task_grouping(self.similarity_matrix, max_k=n_tasks)
        if (
            grouped_list == []
        ):  # 无法通过聚类来进行分组，任务为2个及以下的时候；这里是做多任务的，所以代码默认是2个任务起步
            self.logger.info(
                "无法通过聚类来进行分组，任务为2个及以下的时候, 现在通过阈值来判断是否共享"
            )
            if self.similarity_matrix[0, 1] > self.similarity_threshold:
                self.shared_tasks.append([task_names[0], task_names[1]])
            else:
                self.independent_tasks.append(task_names[0])
                self.independent_tasks.append(task_names[1])
        else:
            self.logger.info(f"通过聚类来进行分组")
            for group in grouped_list:
                if len(group) > 1:
                    group_name = [task_names[i] for i in group]
                    self.shared_tasks.append(group_name)
                else:
                    self.independent_tasks.append(task_names[group[0]])

        # visited = [False for _ in range(n_tasks)]
        # for i in range(n_tasks):
        #     if visited[i]:
        #         continue

        #     # 寻找相似任务
        #     similar_group = [task_names[i]]
        #     visited[i] = True

        #     for j in range(i + 1, n_tasks):
        #         if (
        #             not visited[j]
        #             and self.similarity_matrix[i, j] > self.similarity_threshold
        #         ):
        #             similar_group.append(task_names[j])
        #             visited[j] = True

        #             self.logger.debug(
        #                 f"Tasks '{task_names[i]}' and '{task_names[j]}' are similar: "
        #                 f"similarity = {self.similarity_matrix[i, j]:.3f} > {self.similarity_threshold}"
        #             )

        #     # 分组任务
        #     if len(similar_group) > 1:
        #         self.shared_tasks.append(similar_group)
        #         self.logger.info(f"Shared task group found: {similar_group}")
        #     else:
        #         self.independent_tasks.extend(similar_group)
        #         self.logger.info(f"Independent task: {similar_group[0]}")

        # print("fdf")

    def _assign_task_groups_to_clients(self, clients: List[MultiTaskClient]):
        """
        将任务分组结果分配给客户端

        Args:
            clients: 客户端列表
        """
        for client in clients:

            # 设置共享任务组
            client.shared_tasks = self.shared_tasks.copy()
            client.independent_tasks = self.independent_tasks.copy()
            # 初始化任务
            client.initialize_tasks()

            self.logger.debug(
                f"Client {client.client_id}: Assigned {len(self.shared_tasks)} shared groups, "
                f"{len(self.independent_tasks)} independent tasks"
            )
