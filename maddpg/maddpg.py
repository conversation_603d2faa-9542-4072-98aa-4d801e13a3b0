import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from collections import deque
import random


# Actor-Critic Networks
class Actor(nn.Module):
    def __init__(self, device, state_dim, action_dim):
        super(Actor, self).__init__()
        self.fc1 = nn.Linear(state_dim, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc2_1 = nn.Linear(64, 32)
        self.fc2_2 = nn.Linear(32, 16)
        self.fc3 = nn.Linear(16, action_dim)
        self.to(device)

    def forward(self, x):
        x = torch.tanh(self.fc1(x))
        x = torch.tanh(self.fc2(x))
        x = torch.tanh(self.fc2_1(x))
        x = torch.tanh(self.fc2_2(x))
        return torch.sigmoid(self.fc3(x))


class Critic(nn.Module):
    def __init__(self, device, state_dim, action_dim):
        super(Critic, self).__init__()
        self.fc1 = nn.Linear(state_dim + action_dim, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, 1)
        self.to(device)

    def forward(self, state, action):
        x = torch.cat([state, action], dim=-1)
        x = torch.tanh(self.fc1(x))
        x = torch.tanh(self.fc2(x))
        return self.fc3(x)


# Replay Buffer
class ReplayBuffer:
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)

    def add(self, experience):
        self.buffer.append(experience)

    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)


# MADDPG Framework
class MADDPG:
    def __init__(
        self,
        device: any,
        num_agents: int,
        state_dim: int,
        action_dim: int,
        batch_size: int,
        lr: float,
        gamma: float,
        tau: float,
        capacity: int,
    ):
        self.device = device
        self.num_agents = num_agents
        self.actors = [
            Actor(self.device, state_dim, action_dim) for _ in range(num_agents)
        ]
        self.critics = [
            Critic(self.device, state_dim, action_dim) for _ in range(num_agents)
        ]
        self.target_actors = [
            Actor(self.device, state_dim, action_dim) for _ in range(num_agents)
        ]
        self.target_critics = [
            Critic(self.device, state_dim, action_dim) for _ in range(num_agents)
        ]

        for i in range(num_agents):
            self.target_actors[i].load_state_dict(self.actors[i].state_dict())
            self.target_critics[i].load_state_dict(self.critics[i].state_dict())

        self.optimizers = [
            optim.Adam(self.actors[i].parameters(), lr=lr) for i in range(num_agents)
        ]
        self.critic_optimizers = [
            optim.Adam(self.critics[i].parameters(), lr=lr) for i in range(num_agents)
        ]

        self.replay_buffer = ReplayBuffer(capacity=capacity)
        self.gamma = gamma
        self.tau = tau
        self.batch_size = batch_size

    def update(self):
        if len(self.replay_buffer.buffer) < self.batch_size:
            return  # Not enough experiences to update

        for i in range(self.num_agents):
            experiences = self.replay_buffer.sample(self.batch_size)
            states, actions, rewards, next_states = zip(*experiences)
            states = torch.tensor(
                np.array(states, dtype=np.float32), dtype=torch.float32
            ).to(self.device)
            actions = torch.tensor(
                np.array(actions, dtype=np.float32), dtype=torch.float32
            ).to(self.device)
            rewards = torch.tensor(rewards, dtype=torch.float32).to(self.device)
            next_states = torch.tensor(
                np.array(next_states, dtype=np.float32), dtype=torch.float32
            ).to(self.device)
            # states = torch.tensor(states, dtype=torch.float32).to(self.device)
            # actions = torch.tensor(actions, dtype=torch.float32).to(self.device)
            # next_states = torch.tensor(next_states, dtype=torch.float32).to(self.device)

            # Update Critic
            with torch.no_grad():
                target_actions = self.target_actors[i](next_states)
                target_q = self.target_critics[i](next_states, target_actions).squeeze()
                target_value = rewards + self.gamma * target_q

            current_q = self.critics[i](states, actions).squeeze()
            critic_loss = torch.mean((current_q - target_value) ** 2)
            self.critic_optimizers[i].zero_grad()
            critic_loss.backward()
            self.critic_optimizers[i].step()

            # Update Actor
            actor_loss = -self.critics[i](states, self.actors[i](states)).mean()
            self.optimizers[i].zero_grad()
            actor_loss.backward()
            self.optimizers[i].step()

            # Soft Update Target Networks
            for target_param, param in zip(
                self.target_actors[i].parameters(), self.actors[i].parameters()
            ):
                target_param.data.copy_(
                    self.tau * param.data + (1 - self.tau) * target_param.data
                )
            for target_param, param in zip(
                self.target_critics[i].parameters(), self.critics[i].parameters()
            ):
                target_param.data.copy_(
                    self.tau * param.data + (1 - self.tau) * target_param.data
                )

    def save_checkpoint(self, path):
        for i in range(self.num_agents):
            torch.save(self.actors[i].state_dict(), f"{path}/actor_{i}.pth")
            torch.save(self.critics[i].state_dict(), f"{path}/critic_{i}.pth")

    def load_checkpoint(self, path):
        for i in range(self.num_agents):
            self.actors[i].load_state_dict(torch.load(f"{path}/actor_{i}.pth"))
            self.critics[i].load_state_dict(torch.load(f"{path}/critic_{i}.pth"))

    def get_actions(self, states, add_noise=True):
        """Get actions for all agents given their states"""
        actions = []
        for i in range(self.num_agents):
            state = (
                torch.tensor(states[i], dtype=torch.float32)
                .unsqueeze(0)
                .to(self.device)
            )
            with torch.no_grad():
                action = self.actors[i](state).cpu().numpy().flatten()

            # Add exploration noise
            if add_noise:
                noise = np.random.normal(0, 0.1, size=action.shape)
                action = np.clip(action + noise, 0, 1)

            actions.append(action)
        return actions

    def add_experience(self, states, actions, rewards, next_states):
        """Add experience to replay buffer"""
        experience = (states, actions, rewards, next_states)
        self.replay_buffer.add(experience)

    def can_update(self):
        """Check if we have enough experiences to update"""
        return len(self.replay_buffer.buffer) >= self.batch_size
