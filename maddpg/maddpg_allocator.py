"""
MADDPG Resource Allocator for Multi-Task Federated Learning

This module provides a MADDPG-based resource allocation system that replaces MADDPG
for multi-task federated learning scenarios.
"""

import numpy as np
import torch
import logging
from typing import Dict, List, Any
from entity.entitys import MaddpgConfig, ResourceAllocation
from maddpg.maddpg import MADDPG

logger = logging.getLogger(__name__)


class MADDPGResourceAllocator:
    """
    MADDPG-based resource allocator for multi-task federated learning

    Each client is treated as an agent that needs to make decisions about:
    - Task selection (select_ij)
    - Computing resource allocation (q_ij)
    - Bandwidth allocation (B_ij)
    - Power allocation (P_ij)
    - Task priority (Task_ij)
    """

    def __init__(self, num_clients: int, num_tasks: int, maddpg_config: MaddpgConfig):
        self.num_clients = num_clients
        self.num_tasks = num_tasks
        self.device = maddpg_config.device

        # State dimension: [client_capability, task_requirements, network_conditions, historical_performance]
        # For each client: [compute_capability, bandwidth_capability, power_capability] +
        #                  [network_latency, network_throughput] +
        #                  [last_round_performance]
        # [bandwidth_ij, power_ij, task_ij, q_ij, throughout_ij, latency_ij, packetLoss_ij, execution_time_ij, accuracy, |暂时没放进去  gradient_magnitude]
        # self.state_dim = 9 * num_tasks  # = 9 for 3 tasks
        self.state_dim = maddpg_config.state_dim
        # Action dimension: [select_ij, q_ij, B_ij, P_ij, Task_ij] for each task
        # self.action_dim = 5 * num_tasks  # = 15 for 3 tasks
        self.action_dim = maddpg_config.action_dim

        # Initialize MADDPG
        self.maddpg = MADDPG(
            device=maddpg_config.device,
            num_agents=num_clients,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            batch_size=maddpg_config.batch_size,
            lr=maddpg_config.lr,
            gamma=maddpg_config.gamma,
            tau=maddpg_config.tau,
            capacity=maddpg_config.capacity,
        )

        # Experience storage
        self.last_states = None
        self.last_actions = None

        logger.info(f"MADDPG Resource Allocator initialized:")
        logger.info(f"  Clients: {num_clients}, Tasks: {num_tasks}")
        logger.info(f"  State dim: {self.state_dim}, Action dim: {self.action_dim}")
        logger.info(f"  Device: {self.device}")

    def get_resource_allocations(
        self, states: List[List[float]]
    ) -> Dict[int, ResourceAllocation]:
        """
        Get resource allocations for all clients using MADDPG

        Args:
            states: List of client state vectors (each is a list of 9 floats)

        Returns:
            Dict mapping client_id to ResourceAllocation
        """

        # Convert list states to numpy arrays for MADDPG
        # maddpg_states = [np.array(state, dtype=np.float32) for state in states]

        # Get actions from MADDPG
        actions = self.maddpg.get_actions(states, add_noise=True)

        # Convert actions to ResourceAllocation format
        resource_allocations = self._convert_actions_to_resource_allocations(actions)

        # Store for experience replay
        self.last_states = states
        self.last_actions = actions

        logger.debug(
            f"Generated resource allocations for {len(resource_allocations)} clients"
        )
        return resource_allocations

    def save_checkpoint(self, path: str):
        """Save MADDPG model"""
        self.maddpg.save_checkpoint(path)
        logger.info(f"MADDPG model saved to {path}")

    def update_with_rewards(
        self, rewards: Dict[int, float], next_client_states: List[List[float]]
    ):
        """
        Update MADDPG with rewards and next states

        Args:
            rewards: Dict mapping client_id to reward
            next_client_states: List of next client state vectors
        """
        if self.last_states is None or self.last_actions is None:
            logger.warning("No previous states/actions to update with")
            return

        # Convert next states to numpy arrays
        next_maddpg_states = [
            np.array(state, dtype=np.float32) for state in next_client_states
        ]

        # Convert rewards to list format
        reward_list = [rewards.get(i) for i in range(self.num_clients)]

        # Add experience to replay buffer
        self.maddpg.add_experience(
            states=self.last_states,
            actions=self.last_actions,
            rewards=reward_list,
            next_states=next_maddpg_states,
        )

        # TODO Update networks if we have enough experiences
        if self.maddpg.can_update():
            self.maddpg.update()
            logger.info("MADDPG networks updated successfully")
        else:
            logger.debug(
                f"Not enough experiences for update: {len(self.maddpg.replay_buffer.buffer)}/{self.maddpg.batch_size}"
            )

    def _convert_actions_to_resource_allocations(
        self, actions: List[np.ndarray]
    ) -> Dict[int, ResourceAllocation]:
        """Convert MADDPG actions to ResourceAllocation format"""
        resource_allocations = {}

        for client_id, action in enumerate(actions):
            # Split action into components for each task
            select_ij = {}
            q_ij = {}
            # B_ij = {}
            P_ij = {}
            Task_ij = {}

            for task_id in range(self.num_tasks):
                task_name = f"task_{task_id}"
                base_idx = task_id * 4

                # Extract action components (all in [0,1] range due to sigmoid)
                select_ij[task_name] = float(action[base_idx])
                q_ij[task_name] = float(action[base_idx + 1])
                # B_ij[task_name] = float(action[base_idx + 2])
                P_ij[task_name] = float(action[base_idx + 2])
                Task_ij[task_name] = float(action[base_idx + 3])

            resource_allocations[client_id] = ResourceAllocation(
                select_ij=select_ij, q_ij=q_ij, P_ij=P_ij, Task_ij=Task_ij
            )

        return resource_allocations

    def save_model(self, path: str):
        """Save MADDPG model"""
        self.maddpg.save_checkpoint(path)
        logger.info(f"MADDPG model saved to {path}")

    def load_model(self, path: str):
        """Load MADDPG model"""
        self.maddpg.load_checkpoint(path)
        logger.info(f"MADDPG model loaded from {path}")

    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics"""
        return {
            "replay_buffer_size": len(self.maddpg.replay_buffer.buffer),
            "num_agents": self.num_clients,
            "state_dim": self.state_dim,
            "action_dim": self.action_dim,
        }
