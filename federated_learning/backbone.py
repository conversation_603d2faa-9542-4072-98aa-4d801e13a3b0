import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from transformers import BertTokenizerFast


# -----------------------------
# 1. Shared Transformer Backbone
# -----------------------------
class SharedTransformerBackbone(nn.Module):
    def __init__(
        self,
        d_model=768,
        nhead=12,
        num_layers=6,
        img_patch_size=16,
        img_size=224,
        max_text_len=128,
        vocab_size=30522,
        dropout=0.1,
    ):
        super().__init__()
        self.d_model = d_model

        # 图像参数
        self.img_patch_size = img_patch_size
        self.num_patches = (img_size // img_patch_size) ** 2  # 14x14 = 196
        self.patch_dim = 3 * img_patch_size**2  # 3*16*16 = 768

        # 图像线性投影层（将 patch 映射到 d_model）
        self.patch_embed = nn.Linear(self.patch_dim, d_model)

        # 文本嵌入层
        self.token_embed = nn.Embedding(vocab_size, d_model)

        # 位置编码（可学习）
        self.max_pos_img = self.num_patches + 1  # +1 for [IMG]
        self.max_pos_txt = max_text_len + 1  # +1 for [TXT]
        self.max_pos_total = max(self.max_pos_img, self.max_pos_txt) + 2  # 安全余量

        self.pos_embedding = nn.Embedding(self.max_pos_total, d_model)

        # 模态特定的 cls token
        self.img_cls_token = nn.Parameter(torch.randn(1, 1, d_model))
        self.txt_cls_token = nn.Parameter(torch.randn(1, 1, d_model))

        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=4 * d_model,
            dropout=dropout,
            activation="gelu",
            batch_first=True,
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        self.norm = nn.LayerNorm(d_model)

        # 初始化
        self._reset_parameters()

    def _reset_parameters(self):
        nn.init.xavier_uniform_(self.patch_embed.weight)
        nn.init.xavier_uniform_(self.token_embed.weight)
        nn.init.normal_(self.img_cls_token, std=0.02)
        nn.init.normal_(self.txt_cls_token, std=0.02)

    def forward(self, x, modality="image"):
        """
        Args:
            x: 图像 -> (B, 3, H, W) 或 文本 -> (B, L) token IDs
            modality: 'image' or 'text'
        Returns:
            feature: (B, d_model) 提取的 [CLS] 特征
        """
        B = x.shape[0]

        if modality == "image":
            # 图像处理: (B, 3, 224, 224) -> patch embeddings
            patches = x.unfold(2, self.img_patch_size, self.img_patch_size).unfold(
                3, self.img_patch_size, self.img_patch_size
            )
            patches = patches.reshape(B, 3, self.num_patches, self.patch_dim).transpose(
                1, 2
            )  # (B, N, 3, p*p) -> (B, N, p*p*3)
            patches = patches.reshape(B, self.num_patches, self.patch_dim)
            patches = patches.reshape(B, self.num_patches, self.patch_dim)
            patch_embeds = self.patch_embed(patches)  # (B, N, d_model)

            # 添加 [IMG] token
            cls_tokens = self.img_cls_token.expand(B, -1, -1)  # (B, 1, d_model)
            x_embed = torch.cat([cls_tokens, patch_embeds], dim=1)  # (B, 1+N, d_model)

            # 位置编码
            pos_ids = (
                torch.arange(x_embed.size(1), device=x.device)
                .unsqueeze(0)
                .expand(B, -1)
            )
            pos_embed = self.pos_embedding(pos_ids)
            x = x_embed + pos_embed

        elif modality == "text":
            # 文本处理: (B, L) -> embeddings
            token_embeds = self.token_embed(x)  # (B, L, d_model)

            # 添加 [TXT] token
            cls_tokens = self.txt_cls_token.expand(B, 1, -1)
            x_embed = torch.cat([cls_tokens, token_embeds], dim=1)  # (B, 1+L, d_model)

            # 位置编码
            pos_ids = (
                torch.arange(x_embed.size(1), device=x.device)
                .unsqueeze(0)
                .expand(B, -1)
            )
            pos_embed = self.pos_embedding(pos_ids)
            x = x_embed + pos_embed
        else:
            raise ValueError("modality must be 'image' or 'text'")

        # Transformer 编码
        x = self.transformer(x)  # (B, seq_len, d_model)
        x = self.norm(x)

        # 返回 [CLS] token 的输出
        return x[:, 0, :]  # (B, d_model)


# -----------------------------
# 2. Predictor Heads
# -----------------------------
class ImagePredictor(nn.Module):
    def __init__(self, d_model=768, num_classes=10):
        super().__init__()
        self.head = nn.Sequential(
            nn.Linear(d_model, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes),
        )

    def forward(self, x):
        return self.head(x)


class TextPredictor(nn.Module):
    def __init__(self, d_model=768, num_classes=2):
        super().__init__()
        self.head = nn.Sequential(
            nn.Linear(d_model, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes),
        )

    def forward(self, x):
        return self.head(x)


# -----------------------------
# 3. 整体模型
# -----------------------------
class SharedModel(nn.Module):
    def __init__(self, d_model=768, img_classes=10, txt_classes=2):
        super().__init__()
        self.backbone = SharedTransformerBackbone(d_model=d_model)
        self.img_head = ImagePredictor(d_model, img_classes)
        self.txt_head = TextPredictor(d_model, txt_classes)

    def forward(self, x, modality="image"):
        features = self.backbone(x, modality=modality)
        if modality == "image":
            return self.img_head(features)
        elif modality == "text":
            return self.txt_head(features)
        else:
            raise ValueError("modality must be 'image' or 'text'")


# -----------------------------
# 4. 示例训练代码
# -----------------------------
def train_step(model, data_loader, optimizer, device, modality):
    model.train()
    total_loss = 0.0
    criterion = nn.CrossEntropyLoss()

    for batch in data_loader:
        optimizer.zero_grad()

        if modality == "image":
            images, labels = batch  # 假设 DataLoader 返回 (images, labels)
            images, labels = images.to(device), labels.to(device)
            logits = model(images, modality="image")
        else:  # text
            input_ids, labels = batch
            input_ids, labels = input_ids.to(device), labels.to(device)
            logits = model(input_ids, modality="text")

        loss = criterion(logits, labels)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()

    return total_loss / len(data_loader)


# -----------------------------
# 5. 使用示例
# -----------------------------
if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = SharedModel(d_model=768, img_classes=10, txt_classes=2)
    model.to(device)

    optimizer = optim.AdamW(model.parameters(), lr=1e-4)

    # 这里需要你自己的 Dataset 和 DataLoader
    # img_loader = DataLoader(YourImageDataset(), batch_size=32, shuffle=True)
    # txt_loader = DataLoader(YourTextDataset(), batch_size=32, shuffle=True)

    # 交替训练示例
    # for epoch in range(10):
    #     loss_img = train_step(model, img_loader, optimizer, device, modality='image')
    #     loss_txt = train_step(model, txt_loader, optimizer, device, modality='text')
    #     print(f"Epoch {epoch}: Img Loss={loss_img:.4f}, Txt Loss={loss_txt:.4f}")

    print("模型结构定义完成！")
    print("请根据你的数据集实现对应的 Dataset 和 DataLoader。")
