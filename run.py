"""
Multi-Task Federated Learning with MADDPG

This is the main script to run the complete multi-task federated learning system
with task similarity analysis, MADDPG resource allocation.
"""

from datetime import datetime
import logging
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any

import torch

from multi_task_server import MultiTaskFederatedServer
from entity.entitys import (
    ExperimentConfig,
    MaddpgConfig,
    TaskConfig,
    TaskType,
    MultiTaskFLConfig,
)


def setup_logging(log_level: str = "INFO", log_file: str = "multi_task_fl.log"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler(log_file)],
    )


def create_experiment_config(
    num_clients: int = 5,
    num_rounds: int = 50,
    clients_per_round: int = 3,
    similarity_threshold: float = 0.5,
    network_type: str = "wifi",
    tasks: list = None,
    episodes: int = 100,
    partition_method: str = "hetero",
    partition_alpha: float = 0.5,
    mu: float = 0.01,
) -> Any:
    """Create experiment configuration"""
    # TODO Experiment Config
    # Default tasks
    if tasks is None:
        tasks = ["mnist", "cifar10", "airquality"]

    # Task configurations
    task_configs = {}
    if "mnist" in tasks:
        task_configs["mnist"] = TaskConfig(
            "mnist",
            TaskType.IMAGE_CLASSIFICATION,
            "data/MNIST",
            "cnn",
            batch_size=64,
            learning_rate=0.001,
            local_epochs=clients_per_round,
            priority=1.0,
            output_dim=10,
            freeze_layer=1,
            partition_method=partition_method,
            partition_alpha=partition_alpha,
        )
    if "cifar10" in tasks:
        task_configs["cifar10"] = TaskConfig(
            "cifar10",
            TaskType.IMAGE_CLASSIFICATION,
            "data/cifar-10-batches-py",
            "resnet",
            batch_size=32,
            learning_rate=0.0001,
            local_epochs=clients_per_round,
            priority=1.0,
            output_dim=10,
            freeze_layer=1,
            partition_method=partition_method,
            partition_alpha=partition_alpha,
        )
    if "airquality" in tasks:
        task_configs["airquality"] = TaskConfig(
            "airquality",
            TaskType.TIME_SERIES,
            "data/air_quality",
            "gru",
            batch_size=32,
            learning_rate=0.001,
            local_epochs=clients_per_round,
            priority=0.8,
            partition_method=partition_method,
            partition_alpha=partition_alpha,
        )

    # Multi-task FL configuration (MADDPG will be initialized automatically)
    mt_config = MultiTaskFLConfig(
        num_clients=num_clients,
        num_rounds=num_rounds,
        clients_per_round=clients_per_round,
        similarity_threshold=similarity_threshold,
        tasks=task_configs,
        num_task=len(tasks),
        mu=mu,
    )

    current_time = datetime.now().strftime("%Y%m%d-%H%M%S")

    # [select_ij, power_ij, task_ij, q_ij, energy_use_ij, delay_ij, accuracy_ij]
    # self.state_dim = 7 * num_clients * num_tasks  # = 7 for 3 tasks 在multi_task_server.py中初始化clients再初始化该两个参数
    maddpg_config = MaddpgConfig(
        # state_dim=7 * len(tasks),
        # action_dim=4 * len(tasks),
        episodes=episodes,
        chkpt_dir=f"maddpg_checkpoints/tasks_{len(tasks)}/num_clients_{num_clients}/{current_time}",
    )

    # Main configuration object (compatible with existing FL framework)
    device = torch.device(f"cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    return ExperimentConfig(
        num_clients=num_clients,
        network_type=network_type,
        maddpg_config=maddpg_config,
        mt_config=mt_config,
        current_time=current_time,
        device=device,
    )


def run_experiment(
    config: ExperimentConfig,
    experiment_name: str = "multi_task_fl",
    tensorboard: bool = False,
) -> Dict[str, Any]:
    """Run the multi-task federated learning experiment"""

    logger = logging.getLogger(__name__)
    logger.info("=" * 80)
    logger.info(f"STARTING MULTI-TASK FEDERATED LEARNING EXPERIMENT: {experiment_name}")
    logger.info("=" * 80)

    # Log experiment configuration
    logger.info("Experiment Configuration:")
    logger.info(f"  Number of clients: {config.multi_task.num_clients}")
    logger.info(f"  Number of rounds: {config.multi_task.num_rounds}")
    logger.info(f"  Clients per round: {config.multi_task.clients_per_round}")
    logger.info(f"  Tasks: {list(config.multi_task.tasks.keys())}")
    logger.info(f"  Similarity threshold: {config.multi_task.similarity_threshold}")
    logger.info(f"  Network type: {config.network.type}")
    logger.info(f"  Resource allocation: MADDPG (Multi-Agent DDPG)")

    # Create results directory
    results_dir = Path(config.paths.reports)
    results_dir.mkdir(parents=True, exist_ok=True)

    # Initialize server
    logger.info("Initializing Multi-Task Federated Server...")
    server = MultiTaskFederatedServer(config, tensorboard)

    # Run experiment
    start_time = time.time()

    try:
        # TODO run_experiment
        server.run_experiment()

        experiment_duration = time.time() - start_time
        experiment_duration = experiment_duration / 60
        logger.info(
            f"Experiment completed successfully in {experiment_duration:.2f} minutes"
        )

        # Collect results
        results = {
            "experiment_name": experiment_name,
            "config": {
                "episodes": config.maddpg_config.episodes,
                "state_dim": config.maddpg_config.state_dim,
                "action_dim": config.maddpg_config.action_dim,
                "num_clients": config.multi_task.num_clients,
                "num_rounds": config.multi_task.num_rounds,
                "clients_per_round": config.multi_task.clients_per_round,
                "tasks": list(config.multi_task.tasks.keys()),
                "similarity_threshold": config.multi_task.similarity_threshold,
                "network_type": config.network.type,
            },
            "duration": experiment_duration,
            "episode_metrics": server.episode_metrics,
            "maddpg_stats": server.maddpg_allocator.get_training_stats(),
            "success": True,
        }

        # Save results
        results_file = results_dir / f"{experiment_name}_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"Results saved to: {results_file}")

        return results

    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        import traceback

        traceback.print_exc()

        return {
            "experiment_name": experiment_name,
            "success": False,
            "error": str(e),
            "duration": time.time() - start_time,
        }


def main():
    """Main function"""
    # TODO main()
    parser = argparse.ArgumentParser(
        description="Multi-Task Federated Learning with MADDPG"
    )
    parser.add_argument(
        "--mode",
        choices=["single", "ablation"],
        default="single",
        help="Run mode: single experiment or ablation study",
    )
    parser.add_argument("--num-clients", type=int, default=5, help="Number of clients")
    parser.add_argument("--episodes", type=int, default=2, help="Number of episodes")
    parser.add_argument(
        "--num-rounds", type=int, default=50, help="Number of federated learning rounds"
    )
    parser.add_argument(
        "--clients-per-round", type=int, default=1, help="Number of clients per round"
    )
    parser.add_argument(
        "--similarity-threshold",
        type=float,
        default=0.5,
        help="Task similarity threshold",
    )
    parser.add_argument(
        "--network-type",
        choices=["wifi", "ethernet"],
        default="wifi",
        help="Network type",
    )
    parser.add_argument(
        "--tasks",
        nargs="+",
        default=["mnist", "cifar10"],
        choices=["mnist", "cifar10"],
        help="Tasks to include in the experiment",
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level",
    )
    parser.add_argument(
        "--experiment_name", default="multi_task_fl", help="Experiment name"
    )
    parser.add_argument(
        "--tensorboard",
        action="store_true",
        default=False,
        help="Set True if using TensorBoard",
    )
    parser.add_argument(
        "--partition_method",
        type=str,
        default="hetero",
        metavar="N",
        help="how to partition the dataset on local workers ('hetero' or 'homo')",
    )

    parser.add_argument(
        "--partition_alpha",
        type=float,
        default=0.5,
        metavar="PA",
        help="partition alpha (default: 0.5)",
    )

    parser.add_argument("--mu", type=float, default=0.01, help="proximal term constant")
    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)

    logger = logging.getLogger(__name__)
    logger.info("Multi-Task Federated Learning System")
    logger.info("Task Correlation-Driven AI & Network Co-optimization")
    logger.info("With MADDPG Resource Allocation")

    try:
        if args.mode == "single":
            # Single experiment
            config = create_experiment_config(
                num_clients=args.num_clients,
                num_rounds=args.num_rounds,
                clients_per_round=args.clients_per_round,
                similarity_threshold=args.similarity_threshold,
                network_type=args.network_type,
                tasks=args.tasks,
                episodes=args.episodes,
                partition_method=args.partition_method,
                partition_alpha=args.partition_alpha,
                mu=args.mu,
            )

            result = run_experiment(config, args.experiment_name, args.tensorboard)

            if result["success"]:
                logger.info("Experiment completed successfully!")
            else:
                logger.error("Experiment failed!")

        else:
            logger.info("Ablation study not implemented yet")

    except KeyboardInterrupt:
        logger.info("Experiment interrupted by user")
    except Exception as e:
        logger.error(f"Experiment failed with error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
