from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any


class TaskType(Enum):
    """Task types"""

    IMAGE_CLASSIFICATION = "image"
    TIME_SERIES = "time_series"


@dataclass
class TaskConfig:
    """Configuration for a single task"""

    name: str
    task_type: TaskType
    data_path: str
    model_type: str
    batch_size: int = 64
    learning_rate: float = 0.001
    local_epochs: int = 5
    priority: float = 1.0
    output_dim: int = 10
    freeze_layer: int = 1  # 在FedLPS中，fedlps模型时，freeze_layer=1 或者 2
    partition_method: str = "hetero"
    partition_alpha: float = 0.5


@dataclass
class MultiTaskFLConfig:
    """Configuration for multi-task federated learning"""

    num_clients: int = 5
    num_rounds: int = 100
    clients_per_round: int = 3
    similarity_threshold: float = 0.5

    # Task configurations
    tasks: Dict[str, TaskConfig] = None
    num_task: int = 0
    # Network configuration
    network_config: Any = None
    mu: float = 0.001


@dataclass
class MaddpgConfig:
    device: Any = "cpu"
    batch_size: int = 64
    state_dim: int = 0
    action_dim: int = 0
    lr: float = 0.01
    gamma: float = 0.95
    tau: float = 0.01
    capacity: int = 200000
    episodes: int = 100
    chkpt_dir: str = "maddpg_checkpoints"
    action_threshold: float = 0.3


@dataclass
class ResourceAllocation:
    """Resource allocation from MADDPG"""

    # Client selection: select_ij - whether client i participates in task j
    select_ij: Dict[str, float]  # Task -> Selection probability [0,1]

    # Computing resource allocation: q_ij - computing resources allocated by client i for task j
    q_ij: Dict[str, float]  # Task -> Computing resource allocation [0,1]

    # Bandwidth allocation: B_ij - bandwidth allocated by client i for task j
    # B_ij: Dict[str, float]  # Task -> Bandwidth allocation [0,1]

    # Power allocation: P_ij - power allocated by client i for task j
    P_ij: Dict[str, float]  # Task -> Power allocation [0,1]

    # Start time allocation: Task_ij - task start time allocated by client i for task j
    Task_ij: Dict[str, float]  # Task -> Task priority [0,1]


@dataclass
class NetworkMetrics:
    """Task simulation result"""

    client_id: int
    task_id: int
    task_name: str
    # bandwidth: float  # Actual bandwidth used (MHz)
    power: float  # Actual power used (dBm)
    energy_use: float  # Energy consumption (J)
    energy_up: float  # Energy consumption (J)
    energy_cmp: float  # Energy consumption (J)
    energy_down: float  # Energy consumption (J)
    delay: float  # Average delay (ms)
    up_time: float  # Upload time (s)
    cmp_time: float  # Compute time (s)
    down_time: float  # Download time (s)
    ph_divideby_n0: float
    power_sla_rate: float


class ExperimentConfig:
    def __init__(
        self,
        num_clients: int,
        network_type: str,
        maddpg_config: MaddpgConfig,
        mt_config: MultiTaskFLConfig,
        current_time: str,
        device: Any,
    ):
        self.server = "multi_task"
        self.clients = type("obj", (object,), {"total": num_clients})()
        self.paths = type(
            "obj",
            (object,),
            {
                "model": maddpg_config.chkpt_dir,
                "reports": f"./results/experiment_{current_time}",
                "data": "./data",
            },
        )()
        self.model = type("obj", (object,), {"size": 2048})()

        # Network configuration
        if network_type == "wifi":
            self.network = type(
                "obj",
                (object,),
                {"type": "wifi", "wifi": {"tx_gain": 0.0, "max_packet_size": 1024}},
            )()
        else:
            self.network = type(
                "obj",
                (object,),
                {"type": "ethernet", "ethernet": {"max_packet_size": 1500}},
            )()

        # Data configuration
        self.data = type(
            "obj",
            (object,),
            {"IID": False, "loading": "static"},  # Non-IID for realistic FL
        )()
        self.loader = "basic"

        # Multi-task configuration
        self.multi_task = mt_config
        self.maddpg_config = maddpg_config

        self.device = device
        self.maddpg_config.device = device

        self.current_time = current_time
